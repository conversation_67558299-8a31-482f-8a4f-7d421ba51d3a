[project]
name = "pinboard-api-v2"
version = "0.1.0"
description = "A Python client for interacting with the Pinboard V2 API"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = ":<PERSON>", email = "<EMAIL>"}
]
keywords = ["pinboard", "api", "bookmarks", "client"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Internet :: WWW/HTTP :: Dynamic Content",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
requires-python = ">=3.8"
dependencies = [
    "requests>=2.32.4",
]

[dependency-groups]
dev = [
    "dotenv>=0.9.9",
    "pytest>=8.3.5",
]
